/* 主应用样式 - 现代化CSS框架 */

/* CSS变量定义 */
:root {
    --primary-color: #00A6A6;
    --secondary-color: #993333;
    --background-color: #f5f5f5;
    --text-color: #333;
    --border-color: #ddd;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --white: #ffffff;
    --shadow: 0 2px 4px rgba(0,0,0,0.1);
    --shadow-lg: 0 4px 8px rgba(0,0,0,0.15);
    --border-radius: 4px;
    --transition: all 0.3s ease;
}

/* 基础重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    font-size: 14px;
    line-height: 1.5;
    color: var(--text-color);
    background-color: var(--background-color);
}

/* 主布局 - CSS Grid */
.app-layout {
    display: grid;
    grid-template-rows: auto 1fr;
    height: 100vh;
    overflow: hidden;
}

.app-container {
    display: grid;
    grid-template-columns: 250px 1fr;
    height: 100%;
    overflow: hidden;
}

/* 顶部导航栏 */
.app-header {
    background: var(--primary-color);
    color: var(--white);
    padding: 1rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: var(--shadow);
    z-index: 1000;
}

.header-brand {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.header-brand .logo {
    width: 32px;
    height: 32px;
}

.header-brand h1 {
    font-size: 1.5rem;
    font-weight: 600;
}

.header-user {
    display: flex;
    align-items: center;
    gap: 1rem;
}

/* 侧边栏导航 */
.app-sidebar {
    background: var(--secondary-color);
    color: var(--white);
    overflow-y: auto;
    box-shadow: var(--shadow);
}

.nav-menu {
    list-style: none;
    padding: 1rem 0;
}

.nav-item {
    margin-bottom: 0.5rem;
}

.nav-link {
    display: flex;
    align-items: center;
    padding: 0.75rem 1.5rem;
    color: var(--white);
    text-decoration: none;
    transition: var(--transition);
    border-left: 3px solid transparent;
}

.nav-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
    border-left-color: var(--primary-color);
}

.nav-link.active {
    background-color: rgba(255, 255, 255, 0.2);
    border-left-color: var(--primary-color);
    font-weight: 600;
}

.nav-link i {
    margin-right: 0.75rem;
    width: 16px;
    text-align: center;
}

.nav-submenu {
    list-style: none;
    background-color: rgba(0, 0, 0, 0.1);
    display: none;
}

.nav-item:hover .nav-submenu,
.nav-item.active .nav-submenu {
    display: block;
}

.nav-submenu .nav-link {
    padding-left: 3rem;
    font-size: 0.9rem;
}

/* 主要内容区域 */
.app-main {
    background: var(--background-color);
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.breadcrumb {
    background: var(--white);
    padding: 1rem 2rem;
    border-bottom: 1px solid var(--border-color);
    font-size: 0.9rem;
    color: var(--text-color);
}

.main-content {
    flex: 1;
    padding: 2rem;
    overflow-y: auto;
    background: var(--white);
    margin: 1rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
}

.status-bar {
    background: var(--light-color);
    padding: 0.5rem 2rem;
    border-top: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.85rem;
}

.status-connected {
    color: var(--success-color);
}

.status-disconnected {
    color: var(--danger-color);
}

/* 按钮样式 */
.btn {
    display: inline-block;
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
    font-weight: 500;
    text-align: center;
    text-decoration: none;
    border: 1px solid transparent;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
    background: none;
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: var(--white);
}

.btn-primary:hover {
    background-color: #008a8a;
    border-color: #008a8a;
}

.btn-secondary {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
    color: var(--white);
}

.btn-secondary:hover {
    background-color: #7a2929;
    border-color: #7a2929;
}

.btn-success {
    background-color: var(--success-color);
    border-color: var(--success-color);
    color: var(--white);
}

.btn-danger {
    background-color: var(--danger-color);
    border-color: var(--danger-color);
    color: var(--white);
}

.btn-warning {
    background-color: var(--warning-color);
    border-color: var(--warning-color);
    color: var(--dark-color);
}

/* 加载动画 */
.loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 3rem;
    color: var(--text-color);
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid var(--border-color);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 隐藏类 */
.hidden {
    display: none !important;
}

/* 图标字体 - 简单的CSS图标 */
.icon-network::before { content: "🌐"; }
.icon-device::before { content: "📱"; }
.icon-system::before { content: "⚙️"; }
.icon-time::before { content: "🕐"; }
.icon-auth::before { content: "🔐"; }
