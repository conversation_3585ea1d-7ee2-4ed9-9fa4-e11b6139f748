/**
 * @file main.c
 * @brief WebCfg模块化系统主程序 - 基于libmicrohttpd的HTTP服务器
 * <AUTHOR>
 * @date 2024-01-01
 * @version 1.0
 */

#include <stdio.h>
#include <stdlib.h>
#include <signal.h>
#include <unistd.h>
#include "config_interface.h"
#include "network_config.h"
#include "center_config.h"
#include "gateway_config.h"
#include "recorder_config.h"
#include "sci_config.h"
#include "switch_config.h"
#include "ntp_config.h"
#include "system_mgmt.h"
#include "api_router.h"

// 全局变量
static volatile int g_server_running = 1;

/**
 * @brief 信号处理函数
 */
static void signal_handler(int sig) {
    (void)sig; // 避免未使用参数警告
    printf("\nReceived shutdown signal, stopping server...\n");
    g_server_running = 0;
}

/**
 * @brief 注册API路由 - 按照设计方案注册网络配置API
 */
static int register_api_routes(void) {
    // 注册网络配置API路由 - 按照设计方案的API接口
    if (api_router_register("/api/v1/config/network/selection", HTTP_METHOD_GET,
                           handle_network_selection_get, "获取网络选择配置") != 0) {
        return -1;
    }

    if (api_router_register("/api/v1/config/network/selection", HTTP_METHOD_POST,
                           handle_network_selection_post, "设置网络选择配置") != 0) {
        return -1;
    }

    if (api_router_register("/api/v1/config/network/ethernet", HTTP_METHOD_GET,
                           handle_network_ethernet_get, "获取以太网配置") != 0) {
        return -1;
    }

    if (api_router_register("/api/v1/config/network/ethernet", HTTP_METHOD_POST,
                           handle_network_ethernet_post, "设置以太网配置") != 0) {
        return -1;
    }

    // 注册呼叫中心配置API路由 - 按照设计方案对应0center.c
    if (api_router_register("/api/v1/config/center", HTTP_METHOD_GET,
                           handle_center_get, "获取呼叫中心配置") != 0) {
        return -1;
    }

    if (api_router_register("/api/v1/config/center", HTTP_METHOD_POST,
                           handle_center_post, "设置呼叫中心配置") != 0) {
        return -1;
    }

    // 注册网关配置API路由 - 按照设计方案对应0gateway.c
    if (api_router_register("/api/v1/config/gateway", HTTP_METHOD_GET,
                           handle_gateway_get, "获取网关配置") != 0) {
        return -1;
    }

    if (api_router_register("/api/v1/config/gateway", HTTP_METHOD_POST,
                           handle_gateway_post, "设置网关配置") != 0) {
        return -1;
    }

    // 注册录音配置API路由 - 按照设计方案对应0recorder.c
    if (api_router_register("/api/v1/config/recorder", HTTP_METHOD_GET,
                           handle_recorder_get, "获取录音配置") != 0) {
        return -1;
    }

    if (api_router_register("/api/v1/config/recorder", HTTP_METHOD_POST,
                           handle_recorder_post, "设置录音配置") != 0) {
        return -1;
    }

    // 注册SCI基站配置API路由 - 按照设计方案对应0sci.c
    if (api_router_register("/api/v1/config/sci", HTTP_METHOD_GET,
                           handle_sci_get, "获取SCI基站配置") != 0) {
        return -1;
    }

    if (api_router_register("/api/v1/config/sci", HTTP_METHOD_POST,
                           handle_sci_post, "设置SCI基站配置") != 0) {
        return -1;
    }

    // 注册交换机配置API路由 - 按照设计方案对应0switch.c
    if (api_router_register("/api/v1/config/switch", HTTP_METHOD_GET,
                           handle_switch_get, "获取交换机配置") != 0) {
        return -1;
    }

    if (api_router_register("/api/v1/config/switch", HTTP_METHOD_POST,
                           handle_switch_post, "设置交换机配置") != 0) {
        return -1;
    }

    // 注册NTP配置API路由 - 按照设计方案对应0ntp.c
    if (api_router_register("/api/v1/config/ntp", HTTP_METHOD_GET,
                           handle_ntp_get, "获取NTP配置") != 0) {
        return -1;
    }

    if (api_router_register("/api/v1/config/ntp", HTTP_METHOD_POST,
                           handle_ntp_post, "设置NTP配置") != 0) {
        return -1;
    }

    // 注册系统管理API路由
    if (api_router_register("/api/system/reboot", HTTP_METHOD_POST, handle_system_reboot, "System reboot") != 0) {
        printf("Failed to register system reboot API\n");
        return -1;
    }

    if (api_router_register("/api/system/reset", HTTP_METHOD_POST, handle_system_reset, "System reset") != 0) {
        printf("Failed to register system reset API\n");
        return -1;
    }

    if (api_router_register("/api/system/logs", HTTP_METHOD_GET, handle_system_logs, "System logs") != 0) {
        printf("Failed to register system logs API\n");
        return -1;
    }

    if (api_router_register("/api/system/signal", HTTP_METHOD_GET, handle_system_signal, "3G signal strength") != 0) {
        printf("Failed to register system signal API\n");
        return -1;
    }

    printf("API routes registered successfully\n");
    return 0;
}

/**
 * @brief 主函数 - libmicrohttpd HTTP服务器模式
 */
int main(int argc, char *argv[]) {
    int port = 8080; // 默认端口

    // 解析命令行参数
    if (argc > 1) {
        port = atoi(argv[1]);
        if (port <= 0 || port > 65535) {
            printf("Invalid port number: %s\n", argv[1]);
            return -1;
        }
    }

    printf("WebCfg Modular System v1.0 - libmicrohttpd HTTP Server\n");
    printf("Starting on port %d...\n", port);

    // 设置信号处理
    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);

    // 初始化配置管理器
    if (config_manager_init() != 0) {
        printf("Failed to initialize config manager\n");
        return -1;
    }

    // 初始化网络配置模块
    if (network_config_init() != 0) {
        printf("Failed to initialize network config\n");
        config_manager_cleanup();
        return -1;
    }

    // 初始化呼叫中心配置模块
    if (center_config_init() != 0) {
        printf("Failed to initialize center config\n");
        network_config_cleanup();
        config_manager_cleanup();
        return -1;
    }

    // 初始化网关配置模块
    if (gateway_config_init() != 0) {
        printf("Failed to initialize gateway config\n");
        gateway_config_cleanup();
        center_config_cleanup();
        network_config_cleanup();
        config_manager_cleanup();
        return -1;
    }

    // 初始化录音配置模块
    if (recorder_config_init() != 0) {
        printf("Failed to initialize recorder config\n");
        gateway_config_cleanup();
        center_config_cleanup();
        network_config_cleanup();
        config_manager_cleanup();
        return -1;
    }

    // 初始化SCI配置模块
    if (sci_config_init() != 0) {
        printf("Failed to initialize SCI config\n");
        recorder_config_cleanup();
        gateway_config_cleanup();
        center_config_cleanup();
        network_config_cleanup();
        config_manager_cleanup();
        return -1;
    }

    // 初始化交换机配置模块
    if (switch_config_init() != 0) {
        printf("Failed to initialize switch config\n");
        sci_config_cleanup();
        recorder_config_cleanup();
        gateway_config_cleanup();
        center_config_cleanup();
        network_config_cleanup();
        config_manager_cleanup();
        return -1;
    }

    // 初始化NTP配置模块
    if (ntp_config_init() != 0) {
        printf("Failed to initialize NTP config\n");
        switch_config_cleanup();
        sci_config_cleanup();
        recorder_config_cleanup();
        gateway_config_cleanup();
        center_config_cleanup();
        network_config_cleanup();
        config_manager_cleanup();
        return -1;
    }

    // 初始化系统管理模块
    if (system_mgmt_init() != 0) {
        printf("Failed to initialize system management\n");
        system_mgmt_cleanup();
        ntp_config_cleanup();
        switch_config_cleanup();
        sci_config_cleanup();
        recorder_config_cleanup();
        gateway_config_cleanup();
        center_config_cleanup();
        network_config_cleanup();
        config_manager_cleanup();
        return -1;
    }

    // 初始化API路由系统
    if (api_router_init() != 0) {
        printf("Failed to initialize API router\n");
        gateway_config_cleanup();
        center_config_cleanup();
        network_config_cleanup();
        config_manager_cleanup();
        return -1;
    }

    // 注册API路由
    if (register_api_routes() != 0) {
        printf("Failed to register API routes\n");
        api_router_cleanup();
        system_mgmt_cleanup();
        ntp_config_cleanup();
        switch_config_cleanup();
        sci_config_cleanup();
        recorder_config_cleanup();
        gateway_config_cleanup();
        center_config_cleanup();
        network_config_cleanup();
        config_manager_cleanup();
        return -1;
    }

    // 配置HTTP服务器
    http_server_config_t server_config = {
        .port = port,
        .max_connections = 100,
        .timeout_seconds = 30,
        .document_root = "./web"
    };

    // 启动HTTP服务器
    if (http_server_start(&server_config) != 0) {
        printf("Failed to start HTTP server\n");
        api_router_cleanup();
        system_mgmt_cleanup();
        ntp_config_cleanup();
        switch_config_cleanup();
        sci_config_cleanup();
        recorder_config_cleanup();
        gateway_config_cleanup();
        center_config_cleanup();
        network_config_cleanup();
        config_manager_cleanup();
        return -1;
    }

    printf("System initialized successfully.\n");
    printf("Registered modules: %d\n", config_manager_get_module_count());
    printf("HTTP server running on http://localhost:%d\n", port);
    printf("Press Ctrl+C to stop the server\n");

    // 主循环
    while (g_server_running) {
        sleep(1);
    }

    // 清理资源
    printf("Shutting down...\n");
    http_server_stop();
    api_router_cleanup();
    system_mgmt_cleanup();
    ntp_config_cleanup();
    switch_config_cleanup();
    sci_config_cleanup();
    recorder_config_cleanup();
    gateway_config_cleanup();
    center_config_cleanup();
    network_config_cleanup();
    config_manager_cleanup();

    printf("Server stopped successfully\n");
    return 0;
}