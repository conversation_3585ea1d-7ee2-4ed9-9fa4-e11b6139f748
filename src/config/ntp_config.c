/**
 * @file ntp_config.c
 * @brief NTP时间同步配置管理实现 - 对应旧项目0ntp.c
 * <AUTHOR>
 * @date 2024-01-01
 * @version 1.0
 */

#include "ntp_config.h"
#include "config_interface.h"
#include "file_utils.h"
#include "api_router.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>

// 静态变量（按命名规范）
static config_module_t s_ntp_module;

/**
 * @brief 设置NTP配置默认值
 */
void ntp_config_set_default(cfg_ntp_t *config) {
    if (!config) return;
    
    memset(config, 0, sizeof(cfg_ntp_t));
    strncpy(config->ntp_server, NTP_DEFAULT_SERVER, sizeof(config->ntp_server) - 1);
    config->sync_interval = NTP_DEFAULT_SYNC_INTERVAL;
    config->enable = 1;
    config->timezone_offset = 8;    // 北京时间 UTC+8
    config->auto_sync = 1;
}

/**
 * @brief 加载NTP配置
 */
int ntp_config_load(cfg_ntp_t *config) {
    if (!config) return -1;
    
    // 使用公共工具模块读取二进制配置文件
    if (file_utils_read_binary(NTP_CONFIG_FILE, 0, sizeof(cfg_ntp_t), config) != 0) {
        printf("Warning: Failed to read NTP config, using defaults\n");
        ntp_config_set_default(config);
        return 0; // 使用默认配置不算错误
    }
    
    return 0;
}

/**
 * @brief 保存NTP配置
 */
int ntp_config_save(const cfg_ntp_t *config) {
    if (!config) return -1;
    
    // 验证配置
    if (ntp_config_validate(config) != 0) {
        return -1;
    }
    
    // 使用公共工具模块写入二进制配置文件
    return file_utils_write_binary(NTP_CONFIG_FILE, 0, sizeof(cfg_ntp_t), config);
}

/**
 * @brief 验证NTP配置
 */
int ntp_config_validate(const cfg_ntp_t *config) {
    if (!config) return -1;
    
    // 验证NTP服务器地址不为空
    if (strlen(config->ntp_server) == 0) {
        printf("Invalid NTP server: empty string\n");
        return -1;
    }
    
    // 验证同步间隔范围（60秒到24小时）
    if (config->sync_interval < 60 || config->sync_interval > 86400) {
        printf("Invalid sync interval: %d seconds\n", config->sync_interval);
        return -1;
    }
    
    // 验证时区偏移范围（-12到+12小时）
    if (config->timezone_offset > 24) {
        printf("Invalid timezone offset: %d hours\n", config->timezone_offset);
        return -1;
    }
    
    return 0;
}

/**
 * @brief NTP配置转JSON
 */
int ntp_config_to_json(const cfg_ntp_t *config, cJSON **json) {
    if (!config || !json) return -1;
    
    *json = cJSON_CreateObject();
    if (!*json) return -1;
    
    cJSON_AddStringToObject(*json, "ntp_server", config->ntp_server);
    cJSON_AddNumberToObject(*json, "sync_interval", config->sync_interval);
    cJSON_AddBoolToObject(*json, "enable", config->enable);
    cJSON_AddNumberToObject(*json, "timezone_offset", config->timezone_offset);
    cJSON_AddBoolToObject(*json, "auto_sync", config->auto_sync);
    
    return 0;
}

/**
 * @brief JSON转NTP配置
 */
int ntp_json_to_config(const cJSON *json, cfg_ntp_t *config) {
    if (!json || !config) return -1;
    
    // 先设置默认值
    ntp_config_set_default(config);
    
    // 解析JSON字段
    cJSON *item;
    
    item = cJSON_GetObjectItem(json, "ntp_server");
    if (item && cJSON_IsString(item)) {
        strncpy(config->ntp_server, item->valuestring, sizeof(config->ntp_server) - 1);
        config->ntp_server[sizeof(config->ntp_server) - 1] = '\0';
    }
    
    item = cJSON_GetObjectItem(json, "sync_interval");
    if (item && cJSON_IsNumber(item)) {
        config->sync_interval = (uint32_t)item->valueint;
    }
    
    item = cJSON_GetObjectItem(json, "enable");
    if (item && cJSON_IsBool(item)) {
        config->enable = cJSON_IsTrue(item) ? 1 : 0;
    }
    
    item = cJSON_GetObjectItem(json, "timezone_offset");
    if (item && cJSON_IsNumber(item)) {
        config->timezone_offset = (uint8_t)item->valueint;
    }
    
    item = cJSON_GetObjectItem(json, "auto_sync");
    if (item && cJSON_IsBool(item)) {
        config->auto_sync = cJSON_IsTrue(item) ? 1 : 0;
    }

    return 0;
}

// 配置管理器接口函数（按命名规范）
int ntp_module_load(void *config) {
    return ntp_config_load((cfg_ntp_t *)config);
}

int ntp_module_save(const void *config) {
    return ntp_config_save((const cfg_ntp_t *)config);
}

int ntp_module_validate(const void *config) {
    return ntp_config_validate((const cfg_ntp_t *)config);
}

int ntp_module_to_json(const void *config, cJSON **json) {
    return ntp_config_to_json((const cfg_ntp_t *)config, json);
}

int ntp_module_from_json(const cJSON *json, void *config) {
    return ntp_json_to_config(json, (cfg_ntp_t *)config);
}

// API处理函数（按命名规范）
/**
 * @brief 获取NTP配置API处理函数
 */
int handle_ntp_get(struct MHD_Connection *connection,
                  const char *url,
                  const char *method,
                  const char *upload_data,
                  size_t *upload_data_size,
                  void **con_cls) {
    cfg_ntp_t config;
    cJSON *json_response = NULL;
    char *json_string = NULL;
    struct MHD_Response *response = NULL;

    // 加载配置
    if (ntp_config_load(&config) != 0) {
        const char *error_msg = "{\"error\":\"Failed to load NTP config\"}";
        response = MHD_create_response_from_buffer(strlen(error_msg),
                                                  (void*)error_msg,
                                                  MHD_RESPMEM_PERSISTENT);
        if (response) {
            MHD_add_response_header(response, "Content-Type", "application/json");
            MHD_queue_response(connection, 500, response);
            MHD_destroy_response(response);
            return -1;
        }
        return -1;
    }

    // 转换为JSON
    if (ntp_config_to_json(&config, &json_response) != 0) {
        const char *error_msg = "{\"error\":\"Failed to convert config to JSON\"}";
        response = MHD_create_response_from_buffer(strlen(error_msg),
                                                  (void*)error_msg,
                                                  MHD_RESPMEM_PERSISTENT);
        if (response) {
            MHD_add_response_header(response, "Content-Type", "application/json");
            MHD_queue_response(connection, 500, response);
            MHD_destroy_response(response);
            return -1;
        }
        return -1;
    }

    // 转换JSON为字符串
    json_string = cJSON_Print(json_response);
    cJSON_Delete(json_response);

    if (!json_string) {
        const char *error_msg = "{\"error\":\"Failed to serialize JSON\"}";
        response = MHD_create_response_from_buffer(strlen(error_msg),
                                                  (void*)error_msg,
                                                  MHD_RESPMEM_PERSISTENT);
        if (response) {
            MHD_add_response_header(response, "Content-Type", "application/json");
            MHD_queue_response(connection, 500, response);
            MHD_destroy_response(response);
            return -1;
        }
        return -1;
    }

    // 创建响应
    response = MHD_create_response_from_buffer(strlen(json_string),
                                              json_string,
                                              MHD_RESPMEM_MUST_FREE);
    if (response) {
        MHD_add_response_header(response, "Content-Type", "application/json");
        MHD_queue_response(connection, 200, response);
        MHD_destroy_response(response);
        return 0; // 成功
    } else {
        free(json_string);
        return -1; // 失败
    }
}

/**
 * @brief 设置NTP配置API处理函数
 */
int handle_ntp_post(struct MHD_Connection *connection,
                   const char *url,
                   const char *method,
                   const char *upload_data,
                   size_t *upload_data_size,
                   void **con_cls) {
    cfg_ntp_t config;
    cJSON *request_json = NULL;
    struct MHD_Response *response = NULL;

    // 获取POST数据
    request_json = api_request_get_json_data(connection, upload_data, upload_data_size, con_cls);
    if (!request_json) {
        // 如果数据还在接收中，返回继续处理
        return 0; // 继续处理
    }

    // JSON转配置结构
    if (ntp_json_to_config(request_json, &config) != 0) {
        cJSON_Delete(request_json);
        const char *error_msg = "{\"error\":\"Invalid config data\"}";
        response = MHD_create_response_from_buffer(strlen(error_msg),
                                                  (void*)error_msg,
                                                  MHD_RESPMEM_PERSISTENT);
        if (response) {
            MHD_add_response_header(response, "Content-Type", "application/json");
            MHD_queue_response(connection, 400, response);
            MHD_destroy_response(response);
        }
        return -1;
    }

    cJSON_Delete(request_json);

    // 保存配置
    if (ntp_config_save(&config) != 0) {
        const char *error_msg = "{\"error\":\"Failed to save NTP config\"}";
        response = MHD_create_response_from_buffer(strlen(error_msg),
                                                  (void*)error_msg,
                                                  MHD_RESPMEM_PERSISTENT);
        if (response) {
            MHD_add_response_header(response, "Content-Type", "application/json");
            MHD_queue_response(connection, 500, response);
            MHD_destroy_response(response);
        }
        return -1;
    }

    // 成功响应
    const char *success_msg = "{\"status\":\"success\"}";
    response = MHD_create_response_from_buffer(strlen(success_msg),
                                              (void*)success_msg,
                                              MHD_RESPMEM_PERSISTENT);
    if (response) {
        MHD_add_response_header(response, "Content-Type", "application/json");
        MHD_queue_response(connection, 200, response);
        MHD_destroy_response(response);
        return 0; // 成功
    }

    return -1; // 失败
}

/**
 * @brief 初始化NTP配置模块
 */
int ntp_config_init(void) {
    // 初始化配置模块结构
    s_ntp_module.module_name = "ntp";
    s_ntp_module.config_file_path = NTP_CONFIG_FILE;
    s_ntp_module.config_struct_size = sizeof(cfg_ntp_t);
    s_ntp_module.load_config = ntp_module_load;
    s_ntp_module.save_config = ntp_module_save;
    s_ntp_module.validate_config = ntp_module_validate;
    s_ntp_module.config_to_json = ntp_module_to_json;
    s_ntp_module.json_to_config = ntp_module_from_json;

    // 注册到配置管理器
    if (config_manager_register(&s_ntp_module) != 0) {
        printf("Failed to register NTP config module\n");
        return -1;
    }

    printf("NTP config module initialized\n");
    return 0;
}

/**
 * @brief 清理NTP配置模块
 */
void ntp_config_cleanup(void) {
    printf("NTP config module cleanup\n");
}
