/**
 * @file center_config.c
 * @brief 呼叫中心配置管理模块实现
 * <AUTHOR>
 * @date 2024-01-01
 * @version 1.0
 */

#include "center_config.h"
#include "config_interface.h"
#include "file_utils.h"
#include "network_utils.h"
#include "api_router.h"
#include <stdio.h>
#include <string.h>
#include <arpa/inet.h>

// 静态变量（按命名规范）
static config_module_t s_center_module;

/**
 * @brief 设置默认配置
 */
void center_config_set_default(cfg_center_t *config) {
    if (!config) return;
    
    memset(config, 0, sizeof(cfg_center_t));
    config->server_ip = inet_addr("***********");
    config->server_port = CENTER_DEFAULT_SERVER_PORT;
    config->local_ip = inet_addr("*************");
    config->local_port = CENTER_DEFAULT_LOCAL_PORT;
    config->enable = 1;
}

/**
 * @brief 加载呼叫中心配置
 */
int center_config_load(cfg_center_t *config) {
    if (!config) return -1;
    
    // 使用公共工具模块读取二进制配置文件
    if (file_utils_read_binary(CENTER_CONFIG_FILE, 0, sizeof(cfg_center_t), config) != 0) {
        printf("Warning: Failed to read center config, using defaults\n");
        center_config_set_default(config);
        return 0; // 使用默认配置不算错误
    }
    
    return 0;
}

/**
 * @brief 保存呼叫中心配置
 */
int center_config_save(const cfg_center_t *config) {
    if (!config) return -1;
    
    // 验证配置
    if (center_config_validate(config) != 0) {
        return -1;
    }
    
    // 使用公共工具模块写入二进制配置文件
    return file_utils_write_binary(CENTER_CONFIG_FILE, 0, sizeof(cfg_center_t), config);
}

/**
 * @brief 验证呼叫中心配置
 */
int center_config_validate(const cfg_center_t *config) {
    if (!config) return -1;
    
    // 验证端口范围（uint16_t最大值就是65535，无需检查上限）
    if (config->server_port == 0) {
        return -1;
    }

    if (config->local_port == 0) {
        return -1;
    }
    
    // 验证IP地址（简单检查非零）
    if (config->server_ip == 0 || config->local_ip == 0) {
        return -1;
    }
    
    return 0;
}

/**
 * @brief 配置结构转JSON
 */
int center_config_to_json(const cfg_center_t *config, cJSON **json) {
    if (!config || !json) return -1;
    
    *json = cJSON_CreateObject();
    if (!*json) return -1;
    
    // 使用公共工具模块进行IP地址转换
    char ip_str[16];
    
    if (ip_utils_binary_to_string(config->server_ip, ip_str, sizeof(ip_str)) == 0) {
        cJSON_AddStringToObject(*json, "server_ip", ip_str);
    }
    
    if (ip_utils_binary_to_string(config->local_ip, ip_str, sizeof(ip_str)) == 0) {
        cJSON_AddStringToObject(*json, "local_ip", ip_str);
    }
    
    cJSON_AddNumberToObject(*json, "server_port", config->server_port);
    cJSON_AddNumberToObject(*json, "local_port", config->local_port);
    cJSON_AddBoolToObject(*json, "enable", config->enable ? 1 : 0);
    
    return 0;
}

/**
 * @brief JSON转配置结构
 */
int center_json_to_config(const cJSON *json, cfg_center_t *config) {
    if (!json || !config) return -1;
    
    // 先设置默认值
    center_config_set_default(config);
    
    // 解析JSON字段
    cJSON *item;
    
    item = cJSON_GetObjectItem(json, "server_ip");
    if (item && cJSON_IsString(item)) {
        if (ip_utils_string_to_binary(item->valuestring, &config->server_ip) != 0) {
            return -1;
        }
    }
    
    item = cJSON_GetObjectItem(json, "local_ip");
    if (item && cJSON_IsString(item)) {
        if (ip_utils_string_to_binary(item->valuestring, &config->local_ip) != 0) {
            return -1;
        }
    }
    
    item = cJSON_GetObjectItem(json, "server_port");
    if (item && cJSON_IsNumber(item)) {
        config->server_port = (uint16_t)item->valueint;
    }
    
    item = cJSON_GetObjectItem(json, "local_port");
    if (item && cJSON_IsNumber(item)) {
        config->local_port = (uint16_t)item->valueint;
    }
    
    item = cJSON_GetObjectItem(json, "enable");
    if (item && cJSON_IsBool(item)) {
        config->enable = cJSON_IsTrue(item) ? 1 : 0;
    }
    
    return 0;
}

/**
 * @brief 获取呼叫中心配置信息（API处理函数）
 */
int handle_center_get(struct MHD_Connection *connection, 
                     const char *url, 
                     const char *method,
                     const char *upload_data, 
                     size_t *upload_data_size,
                     void **con_cls) {
    (void)url; (void)method; (void)upload_data; (void)upload_data_size; (void)con_cls;
    
    cfg_center_t config;
    cJSON *json = NULL;
    
    // 加载配置
    if (center_config_load(&config) != 0) {
        api_response_t *response = api_response_create_error(500, "Failed to load center config");
        api_response_send(connection, response);
        api_response_free(response);
        return -1;
    }

    // 转换为JSON
    if (center_config_to_json(&config, &json) != 0) {
        api_response_t *response = api_response_create_error(500, "Failed to convert config to JSON");
        api_response_send(connection, response);
        api_response_free(response);
        return -1;
    }

    // 发送响应
    api_response_t *response = api_response_create_success(json);
    api_response_send(connection, response);
    api_response_free(response);
    cJSON_Delete(json);

    return 0;
}

/**
 * @brief 设置呼叫中心配置信息（API处理函数）
 */
int handle_center_post(struct MHD_Connection *connection, 
                      const char *url, 
                      const char *method,
                      const char *upload_data, 
                      size_t *upload_data_size,
                      void **con_cls) {
    (void)url; (void)method;
    
    // 获取POST数据
    cJSON *json = api_request_get_json_data(connection, upload_data, upload_data_size, con_cls);
    if (!json) {
        api_response_t *response = api_response_create_error(400, "Invalid JSON data");
        api_response_send(connection, response);
        api_response_free(response);
        return -1;
    }

    cfg_center_t config;

    // JSON转配置结构
    if (center_json_to_config(json, &config) != 0) {
        cJSON_Delete(json);
        api_response_t *response = api_response_create_error(400, "Invalid config data");
        api_response_send(connection, response);
        api_response_free(response);
        return -1;
    }

    // 保存配置
    if (center_config_save(&config) != 0) {
        cJSON_Delete(json);
        api_response_t *response = api_response_create_error(500, "Failed to save config");
        api_response_send(connection, response);
        api_response_free(response);
        return -1;
    }

    cJSON_Delete(json);

    // 返回成功响应
    cJSON *success_json = cJSON_CreateObject();
    cJSON_AddStringToObject(success_json, "message", "Center config saved successfully");

    api_response_t *response = api_response_create_success(success_json);
    api_response_send(connection, response);
    api_response_free(response);
    cJSON_Delete(success_json);

    return 0;
}

// 配置模块接口实现（按命名规范）
static int center_module_load(void *config) {
    return center_config_load((cfg_center_t *)config);
}

static int center_module_save(const void *config) {
    return center_config_save((const cfg_center_t *)config);
}

static int center_module_validate(const void *config) {
    return center_config_validate((const cfg_center_t *)config);
}

static int center_module_to_json(const void *config, cJSON **json) {
    return center_config_to_json((const cfg_center_t *)config, json);
}

static int center_module_from_json(const cJSON *json, void *config) {
    return center_json_to_config(json, (cfg_center_t *)config);
}

/**
 * @brief 初始化呼叫中心配置模块
 */
int center_config_init(void) {
    // 初始化配置模块结构
    s_center_module.module_name = "center";
    s_center_module.config_file_path = CENTER_CONFIG_FILE;
    s_center_module.config_struct_size = sizeof(cfg_center_t);
    s_center_module.load_config = center_module_load;
    s_center_module.save_config = center_module_save;
    s_center_module.validate_config = center_module_validate;
    s_center_module.config_to_json = center_module_to_json;
    s_center_module.json_to_config = center_module_from_json;

    // 注册到配置管理器
    if (config_manager_register(&s_center_module) != 0) {
        printf("Failed to register center config module\n");
        return -1;
    }

    printf("Center config module initialized\n");
    return 0;
}

/**
 * @brief 清理呼叫中心配置模块
 */
void center_config_cleanup(void) {
    // 清理模块资源（如果有的话）
    printf("Center config module cleaned up\n");
}
