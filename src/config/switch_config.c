/**
 * @file switch_config.c
 * @brief 交换机配置管理实现 - 对应旧项目0switch.c
 * <AUTHOR>
 * @date 2024-01-01
 * @version 1.0
 */

#include "switch_config.h"
#include "config_interface.h"
#include "file_utils.h"
#include "network_utils.h"
#include "api_router.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <arpa/inet.h>

// 静态变量（按命名规范）
static config_module_t s_switch_module;

/**
 * @brief 设置交换机配置默认值（简化版，符合旧项目）
 */
void switch_config_set_default(cfg_switch_t *config) {
    if (!config) return;

    memset(config, 0, sizeof(cfg_switch_t));
    config->server_ip = inet_addr("***********");
    config->server_port = SWITCH_DEFAULT_SERVER_PORT;
    config->local_ip = inet_addr("*************");
    config->local_port = SWITCH_DEFAULT_LOCAL_PORT;
    config->enable = 1;
    config->timeout = SWITCH_DEFAULT_TIMEOUT;
}

/**
 * @brief 加载交换机配置
 */
int switch_config_load(cfg_switch_t *config) {
    if (!config) return -1;
    
    // 使用公共工具模块读取二进制配置文件
    if (file_utils_read_binary(SWITCH_CONFIG_FILE, 0, sizeof(cfg_switch_t), config) != 0) {
        printf("Warning: Failed to read switch config, using defaults\n");
        switch_config_set_default(config);
        return 0; // 使用默认配置不算错误
    }
    
    return 0;
}

/**
 * @brief 保存交换机配置
 */
int switch_config_save(const cfg_switch_t *config) {
    if (!config) return -1;
    
    // 验证配置
    if (switch_config_validate(config) != 0) {
        return -1;
    }
    
    // 使用公共工具模块写入二进制配置文件
    return file_utils_write_binary(SWITCH_CONFIG_FILE, 0, sizeof(cfg_switch_t), config);
}

/**
 * @brief 验证交换机配置（简化版，符合旧项目）
 */
int switch_config_validate(const cfg_switch_t *config) {
    if (!config) return -1;

    // 验证端口范围
    if (config->server_port == 0) {
        printf("Invalid switch server port: %d\n", config->server_port);
        return -1;
    }

    if (config->local_port == 0) {
        printf("Invalid switch local port: %d\n", config->local_port);
        return -1;
    }

    // 验证超时时间
    if (config->timeout == 0 || config->timeout > 3600) {
        printf("Invalid timeout: %d seconds\n", config->timeout);
        return -1;
    }

    return 0;
}

/**
 * @brief 交换机配置转JSON（简化版，符合旧项目）
 */
int switch_config_to_json(const cfg_switch_t *config, cJSON **json) {
    if (!config || !json) return -1;

    *json = cJSON_CreateObject();
    if (!*json) return -1;

    // 转换IP地址为字符串
    char ip_str[16];

    if (ip_utils_binary_to_string(config->server_ip, ip_str, sizeof(ip_str)) == 0) {
        cJSON_AddStringToObject(*json, "server_ip", ip_str);
    }

    if (ip_utils_binary_to_string(config->local_ip, ip_str, sizeof(ip_str)) == 0) {
        cJSON_AddStringToObject(*json, "local_ip", ip_str);
    }

    cJSON_AddNumberToObject(*json, "server_port", config->server_port);
    cJSON_AddNumberToObject(*json, "local_port", config->local_port);
    cJSON_AddBoolToObject(*json, "enable", config->enable);
    cJSON_AddNumberToObject(*json, "timeout", config->timeout);

    return 0;
}

/**
 * @brief JSON转交换机配置（简化版，符合旧项目）
 */
int switch_json_to_config(const cJSON *json, cfg_switch_t *config) {
    if (!json || !config) return -1;

    // 先设置默认值
    switch_config_set_default(config);

    // 解析JSON字段
    cJSON *item;

    item = cJSON_GetObjectItem(json, "server_ip");
    if (item && cJSON_IsString(item)) {
        if (ip_utils_string_to_binary(item->valuestring, &config->server_ip) != 0) {
            return -1;
        }
    }

    item = cJSON_GetObjectItem(json, "local_ip");
    if (item && cJSON_IsString(item)) {
        if (ip_utils_string_to_binary(item->valuestring, &config->local_ip) != 0) {
            return -1;
        }
    }

    item = cJSON_GetObjectItem(json, "server_port");
    if (item && cJSON_IsNumber(item)) {
        config->server_port = (uint16_t)item->valueint;
    }

    item = cJSON_GetObjectItem(json, "local_port");
    if (item && cJSON_IsNumber(item)) {
        config->local_port = (uint16_t)item->valueint;
    }

    item = cJSON_GetObjectItem(json, "enable");
    if (item && cJSON_IsBool(item)) {
        config->enable = cJSON_IsTrue(item) ? 1 : 0;
    }

    item = cJSON_GetObjectItem(json, "timeout");
    if (item && cJSON_IsNumber(item)) {
        config->timeout = (uint16_t)item->valueint;
    }

    return 0;
}

// 配置管理器接口函数（按命名规范）
int switch_module_load(void *config) {
    return switch_config_load((cfg_switch_t *)config);
}

int switch_module_save(const void *config) {
    return switch_config_save((const cfg_switch_t *)config);
}

int switch_module_validate(const void *config) {
    return switch_config_validate((const cfg_switch_t *)config);
}

int switch_module_to_json(const void *config, cJSON **json) {
    return switch_config_to_json((const cfg_switch_t *)config, json);
}

int switch_module_from_json(const cJSON *json, void *config) {
    return switch_json_to_config(json, (cfg_switch_t *)config);
}

// API处理函数（按命名规范）
/**
 * @brief 获取交换机配置API处理函数
 */
int handle_switch_get(struct MHD_Connection *connection,
                     const char *url,
                     const char *method,
                     const char *upload_data,
                     size_t *upload_data_size,
                     void **con_cls) {
    cfg_switch_t config;
    cJSON *json_response = NULL;
    char *json_string = NULL;
    struct MHD_Response *response = NULL;

    // 加载配置
    if (switch_config_load(&config) != 0) {
        const char *error_msg = "{\"error\":\"Failed to load switch config\"}";
        response = MHD_create_response_from_buffer(strlen(error_msg),
                                                  (void*)error_msg,
                                                  MHD_RESPMEM_PERSISTENT);
        if (response) {
            MHD_add_response_header(response, "Content-Type", "application/json");
            MHD_queue_response(connection, 500, response);
            MHD_destroy_response(response);
            return -1;
        }
        return -1;
    }

    // 转换为JSON
    if (switch_config_to_json(&config, &json_response) != 0) {
        const char *error_msg = "{\"error\":\"Failed to convert config to JSON\"}";
        response = MHD_create_response_from_buffer(strlen(error_msg),
                                                  (void*)error_msg,
                                                  MHD_RESPMEM_PERSISTENT);
        if (response) {
            MHD_add_response_header(response, "Content-Type", "application/json");
            MHD_queue_response(connection, 500, response);
            MHD_destroy_response(response);
            return -1;
        }
        return -1;
    }

    // 转换JSON为字符串
    json_string = cJSON_Print(json_response);
    cJSON_Delete(json_response);

    if (!json_string) {
        const char *error_msg = "{\"error\":\"Failed to serialize JSON\"}";
        response = MHD_create_response_from_buffer(strlen(error_msg),
                                                  (void*)error_msg,
                                                  MHD_RESPMEM_PERSISTENT);
        if (response) {
            MHD_add_response_header(response, "Content-Type", "application/json");
            MHD_queue_response(connection, 500, response);
            MHD_destroy_response(response);
            return -1;
        }
        return -1;
    }

    // 创建响应
    response = MHD_create_response_from_buffer(strlen(json_string),
                                              json_string,
                                              MHD_RESPMEM_MUST_FREE);
    if (response) {
        MHD_add_response_header(response, "Content-Type", "application/json");
        MHD_queue_response(connection, 200, response);
        MHD_destroy_response(response);
        return 0; // 成功
    } else {
        free(json_string);
        return -1; // 失败
    }
}

/**
 * @brief 设置交换机配置API处理函数
 */
int handle_switch_post(struct MHD_Connection *connection,
                      const char *url,
                      const char *method,
                      const char *upload_data,
                      size_t *upload_data_size,
                      void **con_cls) {
    cfg_switch_t config;
    cJSON *request_json = NULL;
    struct MHD_Response *response = NULL;

    // 获取POST数据
    request_json = api_request_get_json_data(connection, upload_data, upload_data_size, con_cls);
    if (!request_json) {
        // 如果数据还在接收中，返回继续处理
        return 0; // 继续处理
    }

    // JSON转配置结构
    if (switch_json_to_config(request_json, &config) != 0) {
        cJSON_Delete(request_json);
        const char *error_msg = "{\"error\":\"Invalid config data\"}";
        response = MHD_create_response_from_buffer(strlen(error_msg),
                                                  (void*)error_msg,
                                                  MHD_RESPMEM_PERSISTENT);
        if (response) {
            MHD_add_response_header(response, "Content-Type", "application/json");
            MHD_queue_response(connection, 400, response);
            MHD_destroy_response(response);
        }
        return -1;
    }

    cJSON_Delete(request_json);

    // 保存配置
    if (switch_config_save(&config) != 0) {
        const char *error_msg = "{\"error\":\"Failed to save switch config\"}";
        response = MHD_create_response_from_buffer(strlen(error_msg),
                                                  (void*)error_msg,
                                                  MHD_RESPMEM_PERSISTENT);
        if (response) {
            MHD_add_response_header(response, "Content-Type", "application/json");
            MHD_queue_response(connection, 500, response);
            MHD_destroy_response(response);
        }
        return -1;
    }

    // 成功响应
    const char *success_msg = "{\"status\":\"success\"}";
    response = MHD_create_response_from_buffer(strlen(success_msg),
                                              (void*)success_msg,
                                              MHD_RESPMEM_PERSISTENT);
    if (response) {
        MHD_add_response_header(response, "Content-Type", "application/json");
        MHD_queue_response(connection, 200, response);
        MHD_destroy_response(response);
        return 0; // 成功
    }

    return -1; // 失败
}

/**
 * @brief 初始化交换机配置模块
 */
int switch_config_init(void) {
    // 初始化配置模块结构
    s_switch_module.module_name = "switch";
    s_switch_module.config_file_path = SWITCH_CONFIG_FILE;
    s_switch_module.config_struct_size = sizeof(cfg_switch_t);
    s_switch_module.load_config = switch_module_load;
    s_switch_module.save_config = switch_module_save;
    s_switch_module.validate_config = switch_module_validate;
    s_switch_module.config_to_json = switch_module_to_json;
    s_switch_module.json_to_config = switch_module_from_json;

    // 注册到配置管理器
    if (config_manager_register(&s_switch_module) != 0) {
        printf("Failed to register switch config module\n");
        return -1;
    }

    printf("Switch config module initialized\n");
    return 0;
}

/**
 * @brief 清理交换机配置模块
 */
void switch_config_cleanup(void) {
    printf("Switch config module cleanup\n");
}
