/**
 * @file switch_config.h
 * @brief 交换机配置管理接口 - 对应旧项目0switch.c
 * <AUTHOR>
 * @date 2024-01-01
 * @version 1.0
 */

#ifndef SWITCH_CONFIG_H
#define SWITCH_CONFIG_H

#include <stdint.h>
#include <cjson/cJSON.h>
#include "api_router.h"

// 交换机配置文件路径 - 兼容旧项目
#define SWITCH_CONFIG_FILE "/home/<USER>/cfg/switch.cfg"

// 交换机模块默认配置值（简化版，符合旧项目）
#define SWITCH_DEFAULT_SERVER_PORT      7000
#define SWITCH_DEFAULT_LOCAL_PORT       7001
#define SWITCH_DEFAULT_TIMEOUT          30

/**
 * @brief 交换机配置结构体 - 严格兼容旧项目stCfgSwitch（简化版）
 * 移除了旧项目中不存在的高级功能，保持简单的配置读写
 */
typedef struct {
    uint32_t server_ip;         // 交换服务器IP地址
    uint16_t server_port;       // 交换服务器端口
    uint32_t local_ip;          // 本地IP地址
    uint16_t local_port;        // 本地端口
    uint8_t  enable;            // 交换功能启用标志
    uint16_t timeout;           // 超时时间（秒）
    uint8_t  reserved[9];       // 保留字段，保持结构体对齐
} cfg_switch_t;

/**
 * @brief 设置交换机配置默认值
 * @param config 交换机配置结构指针
 */
void switch_config_set_default(cfg_switch_t *config);

/**
 * @brief 加载交换机配置
 * @param config 交换机配置结构指针
 * @return 0成功，-1失败
 */
int switch_config_load(cfg_switch_t *config);

/**
 * @brief 保存交换机配置
 * @param config 交换机配置结构指针
 * @return 0成功，-1失败
 */
int switch_config_save(const cfg_switch_t *config);

/**
 * @brief 验证交换机配置
 * @param config 交换机配置结构指针
 * @return 0有效，-1无效
 */
int switch_config_validate(const cfg_switch_t *config);

/**
 * @brief 交换机配置转JSON
 * @param config 交换机配置结构指针
 * @param json JSON对象指针的指针
 * @return 0成功，-1失败
 */
int switch_config_to_json(const cfg_switch_t *config, cJSON **json);

/**
 * @brief JSON转交换机配置
 * @param json JSON对象指针
 * @param config 交换机配置结构指针
 * @return 0成功，-1失败
 */
int switch_json_to_config(const cJSON *json, cfg_switch_t *config);

// 配置管理器接口函数（按命名规范）
int switch_module_load(void *config);
int switch_module_save(const void *config);
int switch_module_validate(const void *config);
int switch_module_to_json(const void *config, cJSON **json);
int switch_module_from_json(const cJSON *json, void *config);

// API处理函数（按命名规范）
/**
 * @brief 获取交换机配置API处理函数
 * @param connection HTTP连接
 * @param url 请求URL
 * @param method HTTP方法
 * @param upload_data 上传数据
 * @param upload_data_size 上传数据大小
 * @param con_cls 连接状态
 * @return 0成功，-1失败
 */
int handle_switch_get(struct MHD_Connection *connection,
                     const char *url,
                     const char *method,
                     const char *upload_data,
                     size_t *upload_data_size,
                     void **con_cls);

/**
 * @brief 设置交换机配置API处理函数
 * @param connection HTTP连接
 * @param url 请求URL
 * @param method HTTP方法
 * @param upload_data 上传数据
 * @param upload_data_size 上传数据大小
 * @param con_cls 连接状态
 * @return 0成功，-1失败
 */
int handle_switch_post(struct MHD_Connection *connection,
                      const char *url,
                      const char *method,
                      const char *upload_data,
                      size_t *upload_data_size,
                      void **con_cls);

/**
 * @brief 初始化交换机配置模块
 * @return 0成功，-1失败
 */
int switch_config_init(void);

/**
 * @brief 清理交换机配置模块
 */
void switch_config_cleanup(void);

#endif // SWITCH_CONFIG_H
