/**
 * @file ntp_config.h
 * @brief NTP时间同步配置管理接口 - 对应旧项目0ntp.c
 * <AUTHOR>
 * @date 2024-01-01
 * @version 1.0
 */

#ifndef NTP_CONFIG_H
#define NTP_CONFIG_H

#include <stdint.h>
#include <cjson/cJSON.h>
#include "api_router.h"

// NTP配置文件路径 - 兼容旧项目
#define NTP_CONFIG_FILE "/home/<USER>/cfg/ntp.cfg"

// NTP模块默认配置值
#define NTP_DEFAULT_SERVER      "pool.ntp.org"
#define NTP_DEFAULT_SYNC_INTERVAL   3600    // 1小时同步一次

/**
 * @brief NTP配置结构体 - 兼容旧项目stCfgNtp
 */
typedef struct {
    char     ntp_server[64];        // NTP服务器地址
    uint32_t sync_interval;         // 同步间隔（秒）
    uint8_t  enable;                // NTP功能启用标志
    uint8_t  timezone_offset;       // 时区偏移（小时）
    uint8_t  auto_sync;             // 自动同步标志
    uint8_t  reserved[13];          // 保留字段，保持结构体对齐
} cfg_ntp_t;

/**
 * @brief 设置NTP配置默认值
 * @param config NTP配置结构指针
 */
void ntp_config_set_default(cfg_ntp_t *config);

/**
 * @brief 加载NTP配置
 * @param config NTP配置结构指针
 * @return 0成功，-1失败
 */
int ntp_config_load(cfg_ntp_t *config);

/**
 * @brief 保存NTP配置
 * @param config NTP配置结构指针
 * @return 0成功，-1失败
 */
int ntp_config_save(const cfg_ntp_t *config);

/**
 * @brief 验证NTP配置
 * @param config NTP配置结构指针
 * @return 0有效，-1无效
 */
int ntp_config_validate(const cfg_ntp_t *config);

/**
 * @brief NTP配置转JSON
 * @param config NTP配置结构指针
 * @param json JSON对象指针的指针
 * @return 0成功，-1失败
 */
int ntp_config_to_json(const cfg_ntp_t *config, cJSON **json);

/**
 * @brief JSON转NTP配置
 * @param json JSON对象指针
 * @param config NTP配置结构指针
 * @return 0成功，-1失败
 */
int ntp_json_to_config(const cJSON *json, cfg_ntp_t *config);

// 配置管理器接口函数（按命名规范）
int ntp_module_load(void *config);
int ntp_module_save(const void *config);
int ntp_module_validate(const void *config);
int ntp_module_to_json(const void *config, cJSON **json);
int ntp_module_from_json(const cJSON *json, void *config);

// API处理函数（按命名规范）
/**
 * @brief 获取NTP配置API处理函数
 * @param connection HTTP连接
 * @param url 请求URL
 * @param method HTTP方法
 * @param upload_data 上传数据
 * @param upload_data_size 上传数据大小
 * @param con_cls 连接状态
 * @return 0成功，-1失败
 */
int handle_ntp_get(struct MHD_Connection *connection,
                  const char *url,
                  const char *method,
                  const char *upload_data,
                  size_t *upload_data_size,
                  void **con_cls);

/**
 * @brief 设置NTP配置API处理函数
 * @param connection HTTP连接
 * @param url 请求URL
 * @param method HTTP方法
 * @param upload_data 上传数据
 * @param upload_data_size 上传数据大小
 * @param con_cls 连接状态
 * @return 0成功，-1失败
 */
int handle_ntp_post(struct MHD_Connection *connection,
                   const char *url,
                   const char *method,
                   const char *upload_data,
                   size_t *upload_data_size,
                   void **con_cls);

/**
 * @brief 初始化NTP配置模块
 * @return 0成功，-1失败
 */
int ntp_config_init(void);

/**
 * @brief 清理NTP配置模块
 */
void ntp_config_cleanup(void);

#endif // NTP_CONFIG_H
