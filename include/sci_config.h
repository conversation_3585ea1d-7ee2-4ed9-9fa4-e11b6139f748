/**
 * @file sci_config.h
 * @brief SCI基站配置管理接口 - 对应旧项目0sci.c
 * <AUTHOR>
 * @date 2024-01-01
 * @version 1.0
 */

#ifndef SCI_CONFIG_H
#define SCI_CONFIG_H

#include <stdint.h>
#include <cJSON.h>
#include "api_router.h"

// SCI配置文件路径 - 兼容旧项目
#define SCI_CONFIG_FILE "/home/<USER>/cfg/sci.cfg"

// SCI模块默认配置值（简化版，符合旧项目）
#define SCI_DEFAULT_SERVER_PORT     9000
#define SCI_DEFAULT_LOCAL_PORT      9001
#define SCI_DEFAULT_TIMEOUT         30

/**
 * @brief SCI基站配置结构体 - 严格兼容旧项目stCfgSci（简化版）
 * 移除了旧项目中不存在的复杂功能，保持简单的配置读写
 */
typedef struct {
    uint32_t server_ip;         // SCI服务器IP地址
    uint16_t server_port;       // SCI服务器端口
    uint32_t local_ip;          // 本地IP地址
    uint16_t local_port;        // 本地端口
    uint8_t  enable;            // SCI功能启用标志
    uint8_t  device_type;       // 设备类型：0-3G基站，1-4G基站
    uint16_t timeout;           // 超时时间（秒）
    uint8_t  reserved[9];       // 保留字段，保持结构体对齐
} cfg_sci_t;

/**
 * @brief 设置SCI配置默认值
 * @param config SCI配置结构指针
 */
void sci_config_set_default(cfg_sci_t *config);

/**
 * @brief 加载SCI配置
 * @param config SCI配置结构指针
 * @return 0成功，-1失败
 */
int sci_config_load(cfg_sci_t *config);

/**
 * @brief 保存SCI配置
 * @param config SCI配置结构指针
 * @return 0成功，-1失败
 */
int sci_config_save(const cfg_sci_t *config);

/**
 * @brief 验证SCI配置
 * @param config SCI配置结构指针
 * @return 0有效，-1无效
 */
int sci_config_validate(const cfg_sci_t *config);

/**
 * @brief SCI配置转JSON
 * @param config SCI配置结构指针
 * @param json JSON对象指针的指针
 * @return 0成功，-1失败
 */
int sci_config_to_json(const cfg_sci_t *config, cJSON **json);

/**
 * @brief JSON转SCI配置
 * @param json JSON对象指针
 * @param config SCI配置结构指针
 * @return 0成功，-1失败
 */
int sci_json_to_config(const cJSON *json, cfg_sci_t *config);

// 配置管理器接口函数（按命名规范）
int sci_module_load(void *config);
int sci_module_save(const void *config);
int sci_module_validate(const void *config);
int sci_module_to_json(const void *config, cJSON **json);
int sci_module_from_json(const cJSON *json, void *config);

// API处理函数（按命名规范）
/**
 * @brief 获取SCI配置API处理函数
 * @param connection HTTP连接
 * @param url URL路径
 * @param method HTTP方法
 * @param upload_data 上传数据
 * @param upload_data_size 上传数据大小指针
 * @param con_cls 连接类指针
 * @return 0成功，-1失败
 */
int handle_sci_get(struct MHD_Connection *connection,
                  const char *url,
                  const char *method,
                  const char *upload_data,
                  size_t *upload_data_size,
                  void **con_cls);

/**
 * @brief 设置SCI配置API处理函数
 * @param connection HTTP连接
 * @param url URL路径
 * @param method HTTP方法
 * @param upload_data 上传数据
 * @param upload_data_size 上传数据大小指针
 * @param con_cls 连接类指针
 * @return 0成功，-1失败
 */
int handle_sci_post(struct MHD_Connection *connection,
                   const char *url,
                   const char *method,
                   const char *upload_data,
                   size_t *upload_data_size,
                   void **con_cls);

/**
 * @brief 初始化SCI配置模块
 * @return 0成功，-1失败
 */
int sci_config_init(void);

/**
 * @brief 清理SCI配置模块
 */
void sci_config_cleanup(void);

#endif // SCI_CONFIG_H
