/**
 * @file recorder_config.h
 * @brief 录音模块配置管理接口 - 对应旧项目0recorder.c
 * <AUTHOR>
 * @date 2024-01-01
 * @version 1.0
 */

#ifndef RECORDER_CONFIG_H
#define RECORDER_CONFIG_H

#include <stdint.h>
#include <cJSON.h>
#include "api_router.h"

// 录音配置文件路径 - 兼容旧项目
#define RECORDER_CONFIG_FILE "/home/<USER>/cfg/record.cfg"

// 录音模块默认配置值
#define RECORDER_DEFAULT_SERVER_PORT    8000
#define RECORDER_DEFAULT_LOCAL_PORT     8001
#define RECORDER_DEFAULT_TIMEOUT        30
#define RECORDER_DEFAULT_BUFFER_SIZE    1024

/**
 * @brief 录音配置结构体 - 兼容旧项目stCfgRecorder
 */
typedef struct {
    uint32_t server_ip;         // 录音服务器IP地址
    uint16_t server_port;       // 录音服务器端口
    uint32_t local_ip;          // 本地IP地址
    uint16_t local_port;        // 本地端口
    uint8_t  enable;            // 录音功能启用标志
    uint8_t  record_mode;       // 录音模式：0-手动，1-自动
    uint8_t  audio_format;      // 音频格式：0-PCM，1-G711A，2-G711U
    uint8_t  sample_rate;       // 采样率：0-8K，1-16K，2-32K
    uint16_t buffer_size;       // 缓冲区大小
    uint16_t timeout;           // 超时时间（秒）
    char     storage_path[64];  // 录音文件存储路径
    char     file_prefix[32];   // 录音文件前缀
} cfg_recorder_t;

/**
 * @brief 设置录音配置默认值
 * @param config 录音配置结构指针
 */
void recorder_config_set_default(cfg_recorder_t *config);

/**
 * @brief 加载录音配置
 * @param config 录音配置结构指针
 * @return 0成功，-1失败
 */
int recorder_config_load(cfg_recorder_t *config);

/**
 * @brief 保存录音配置
 * @param config 录音配置结构指针
 * @return 0成功，-1失败
 */
int recorder_config_save(const cfg_recorder_t *config);

/**
 * @brief 验证录音配置
 * @param config 录音配置结构指针
 * @return 0有效，-1无效
 */
int recorder_config_validate(const cfg_recorder_t *config);

/**
 * @brief 录音配置转JSON
 * @param config 录音配置结构指针
 * @param json JSON对象指针的指针
 * @return 0成功，-1失败
 */
int recorder_config_to_json(const cfg_recorder_t *config, cJSON **json);

/**
 * @brief JSON转录音配置
 * @param json JSON对象指针
 * @param config 录音配置结构指针
 * @return 0成功，-1失败
 */
int recorder_json_to_config(const cJSON *json, cfg_recorder_t *config);

// 配置管理器接口函数（按命名规范）
int recorder_module_load(void *config);
int recorder_module_save(const void *config);
int recorder_module_validate(const void *config);
int recorder_module_to_json(const void *config, cJSON **json);
int recorder_module_from_json(const cJSON *json, void *config);

// API处理函数（按命名规范）
/**
 * @brief 获取录音配置API处理函数
 * @param connection HTTP连接
 * @param url URL路径
 * @param method HTTP方法
 * @param upload_data 上传数据
 * @param upload_data_size 上传数据大小指针
 * @param con_cls 连接类指针
 * @return 0成功，-1失败
 */
int handle_recorder_get(struct MHD_Connection *connection,
                       const char *url,
                       const char *method,
                       const char *upload_data,
                       size_t *upload_data_size,
                       void **con_cls);

/**
 * @brief 设置录音配置API处理函数
 * @param connection HTTP连接
 * @param url URL路径
 * @param method HTTP方法
 * @param upload_data 上传数据
 * @param upload_data_size 上传数据大小指针
 * @param con_cls 连接类指针
 * @return 0成功，-1失败
 */
int handle_recorder_post(struct MHD_Connection *connection,
                        const char *url,
                        const char *method,
                        const char *upload_data,
                        size_t *upload_data_size,
                        void **con_cls);

/**
 * @brief 初始化录音配置模块
 * @return 0成功，-1失败
 */
int recorder_config_init(void);

/**
 * @brief 清理录音配置模块
 */
void recorder_config_cleanup(void);

#endif // RECORDER_CONFIG_H
