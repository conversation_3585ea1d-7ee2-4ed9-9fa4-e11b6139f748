# 重构方案设计文档

## 1. 重构目标

### 1.1 主要目标
- **100%功能兼容**: 保持所有现有功能不变，**严禁增加新功能**
- **前后端分离**: 解耦前后端，提供REST API接口
- **现代化界面**: 使用现代Web技术重构用户界面
- **构建系统升级**: 使用cmake替代Makefile构建系统
- **多平台支持**: 保持对所有现有硬件平台的支持
- **模块化设计**: 便于维护，但**不改变功能边界**

### 1.2 严格约束条件
- **功能约束**: 不修改第三方库源码(cgic、cJSON、microhttpd等)
- **兼容性约束**: 保持配置文件格式完全兼容
- **逻辑约束**: 保持业务逻辑不变，**严禁增加复杂的业务规则**
- **界面约束**: 支持中英文界面切换，优化布局，方便切换模块配置界面。
- **构建约束**: 第三方库放置在third_party目录，使用各自原生构建系统
- **复杂度约束**: **新系统复杂度不能超过旧系统**
- **一致性约束**: **根据功能一致性检查报告(05_功能一致性检查报告.md)，严格控制配置结构复杂度**

## 2. 命名规范

### 2.1 文件和目录命名规范

#### 2.1.1 目录命名
- **源码目录**：使用小写字母和下划线，如 `src/`, `config/`, `utils/`
- **模块目录**：按功能模块命名，如 `network/`, `system/`, `auth/`
- **第三方库目录**：使用原库名，如 `third_party/cjson/`, `third_party/microhttpd/`

#### 2.1.2 文件命名
- **C源文件**：模块名_功能.c，如 `network_config.c`, `system_mgmt.c`
- **C头文件**：模块名_功能.h，如 `network_config.h`, `system_mgmt.h`
- **配置文件**：功能名.cfg，如 `network.cfg`, `center.cfg`
- **脚本文件**：功能描述.sh，如 `build_all.sh`, `deploy.sh`

### 2.2 代码命名规范

#### 2.2.1 函数命名
- **API处理函数**：`handle_模块_操作`，如 `handle_network_get()`, `handle_system_reboot()`
- **配置操作函数**：`模块_config_操作`，如 `network_config_load()`, `center_config_save()`
- **工具函数**：`工具类型_操作`，如 `file_utils_read()`, `ip_utils_validate()`
- **系统函数**：`system_操作`，如 `system_reboot()`, `system_reset()`

#### 2.2.2 变量命名
- **全局变量**：`g_变量名`，如 `g_server_port`, `g_config_path`
- **静态变量**：`s_变量名`，如 `s_api_routes`, `s_config_modules`
- **结构体成员**：小写字母和下划线，如 `server_ip`, `listen_port`
- **宏定义**：全大写字母和下划线，如 `MAX_CONFIG_SIZE`, `DEFAULT_PORT`

#### 2.2.3 结构体和类型命名
- **配置结构体**：`cfg_模块_t`，如 `cfg_network_t`, `cfg_center_t`
- **API结构体**：`api_功能_t`，如 `api_route_t`, `api_response_t`
- **工具结构体**：`utils_功能_t`，如 `utils_file_t`, `utils_json_t`
- **枚举类型**：`enum_功能_e`，如 `config_type_e`, `api_method_e`

#### 2.2.4 API路径命名
- **配置API**：`/config/模块名`，如 `/config/network`, `/config/center`
- **系统API**：`/system/操作`，如 `/system/reboot`, `/system/logs`
- **认证API**：`/auth/操作`，如 `/auth/login`, `/auth/password`

### 2.3 注释规范

#### 2.3.1 文件头注释
```c
/**
 * @file network_config.c
 * @brief 网络配置管理模块
 * <AUTHOR>
 * @date 2024-01-01
 * @version 1.0
 */
```

#### 2.3.2 函数注释
```c
/**
 * @brief 获取网络配置信息
 * @param response JSON响应对象指针
 * @return 0成功，-1失败
 */
int handle_network_get(cJSON **response);
```

## 3. 总体架构设计

### 3.1 架构概览

```
┌─────────────────────────────────────────────────────────────┐
│                    前端 (Web Frontend)                       │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐ │
│  │   HTML5 Pages   │ │   CSS3 Styles   │ │   JavaScript    │ │
│  └─────────────────┘ └─────────────────┘ └─────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                               │ HTTP/JSON
                               ▼
┌─────────────────────────────────────────────────────────────┐
│                  后端 API服务 (Backend API)                  │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐ │
│  │   API Gateway   │ │  Config Handler │ │  Auth Handler   │ │
│  └─────────────────┘ └─────────────────┘ └─────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                               │
                               ▼
┌─────────────────────────────────────────────────────────────┐
│                 业务逻辑层 (Business Logic)                  │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐ │
│  │ Network Config  │ │  Device Config  │ │  System Config  │ │
│  └─────────────────┘ └─────────────────┘ └─────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                               │
                               ▼
┌─────────────────────────────────────────────────────────────┐
│                 数据访问层 (Data Access)                     │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐ │
│  │  Binary Config  │ │   INI Config    │ │  System Files   │ │
│  └─────────────────┘ └─────────────────┘ └─────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 2.2 模块分层设计

#### 2.2.1 前端层 (Presentation Layer)
- **技术栈**: HTML5 + CSS3 + JavaScript
- **布局**: 响应式布局替代frameset
- **交互**: 基于fetch API的异步通信
- **样式**: 现代化UI设计，保持功能区域布局

#### 2.2.2 API服务层 (API Service Layer)
- **技术栈**: C语言 + libmicrohttpd HTTP服务器
- **协议**: RESTful API + JSON数据格式
- **认证**: 基于token的身份验证
- **路由**: 统一的API路由管理

#### 2.2.3 业务逻辑层 (Business Logic Layer)
- **复用策略**: 最大化复用现有业务逻辑代码，**不增加新的业务规则**
- **重构范围**: 仅重构接口层，**保持核心逻辑完全不变**
- **模块化**: 按功能模块重新组织代码结构，**但功能边界与旧CGI一致**
- **简单性**: **保持旧项目的简单性，不引入复杂的业务流程**

#### 2.2.4 数据访问层 (Data Access Layer)
- **兼容性**: 完全保持现有配置文件格式，**不增加新的配置项**
- **封装**: 提供统一的配置读写接口，**功能与旧CGI的文件操作一致**
- **事务性**: 保证配置操作的原子性，**但不增加复杂的事务管理**

## 3. 技术选型方案

### 3.1 后端技术选型

#### 3.1.1 HTTP服务器
**选择方案**: libmicrohttpd
- 轻量级、高性能
- 支持多线程
- API简洁易用
- 使用autotools原生构建系统
- 静态库编译，减少依赖

#### 3.1.2 JSON处理
**选择方案**: cJSON库
- 轻量级JSON解析库
- C语言原生支持
- API简单直观
- 内存占用小
- 使用cmake原生构建系统
- 静态库编译，减少依赖

#### 3.1.3 简化的API框架设计
基于可行性分析，采用更简化的API设计：

```c
// 简化的API路由设计
typedef struct {
    char *path;                    // API路径
    int (*get_handler)(cJSON **);  // GET处理函数
    int (*post_handler)(cJSON *);  // POST处理函数
} simple_api_route_t;

// 简化的路由表（按命名规范）
static simple_api_route_t s_api_routes[] = {
    {"/config/network",  handle_network_get,  handle_network_post},
    {"/config/center",   handle_center_get,   handle_center_post},
    {"/config/gateway",  handle_gateway_get,  handle_gateway_post},
    {"/config/recorder", handle_recorder_get, handle_recorder_post},
    {"/config/sci",      handle_sci_get,      handle_sci_post},
    {"/config/switch",   handle_switch_get,   handle_switch_post},
    {"/config/ntp",      handle_ntp_get,      handle_ntp_post},
    {"/system/reboot",   NULL,                handle_system_reboot},
    {"/system/reset",    NULL,                handle_system_reset},
    {"/system/logs",     handle_system_logs,  NULL},
    {"/system/signal",   handle_system_signal, NULL},
    {"/auth/password",   NULL,                handle_auth_password},
};

// 统一的配置操作接口（按命名规范）
typedef struct {
    char *module_name;           // 模块名称
    char *config_file_path;      // 配置文件路径
    size_t config_struct_size;   // 配置结构大小
    int (*json_to_config)(cJSON *, void *);  // JSON转配置结构
    int (*config_to_json)(void *, cJSON **); // 配置结构转JSON
    int (*validate_config)(void *);          // 配置验证
} config_module_t;
```

### 3.2 前端技术选型

#### 3.2.1 页面布局
**HTML5结构设计**:
```html
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VICTEL IP交换机配置系统</title>
</head>
<body>
    <header class="app-header">
        <!-- 顶部导航 -->
    </header>
    <div class="app-container">
        <aside class="app-sidebar">
            <!-- 侧边栏菜单 -->
        </aside>
        <main class="app-main">
            <!-- 主要内容区域 -->
        </main>
    </div>
</body>
</html>
```

#### 3.2.2 统一CSS框架
**自定义CSS框架**:
- 基于CSS Grid和Flexbox布局
- 响应式设计支持
- 提升视觉体验

#### 3.2.3 统一JavaScript架构
**模块化JavaScript设计**:
```javascript
// 主应用模块
const App = {
    config: {},
    api: {},
    ui: {},
    utils: {},
    init: function() {
        // 应用初始化
    }
};

// API通信模块
const API = {
    baseURL: '/api/v1',
    request: async function(method, url, data) {
        // 统一API请求处理
    },
    config: {
        get: () => API.request('GET', '/config'),
        save: (data) => API.request('POST', '/config', data)
    }
};
```

### 3.3 混合构建系统

#### 3.3.1 主项目CMake配置
```cmake
cmake_minimum_required(VERSION 3.12)
project(webcfg_system)

# 平台检测
if(CMAKE_SYSTEM_PROCESSOR MATCHES "arm")
    if(DEFINED ENV{CROSS_COMPILE})
        set(CMAKE_C_COMPILER $ENV{CROSS_COMPILE}gcc)
        set(CMAKE_CXX_COMPILER $ENV{CROSS_COMPILE}g++)
    endif()
endif()

# 编译选项
set(CMAKE_C_STANDARD 99)
set(CMAKE_C_FLAGS "-Wall -O2")

# 查找预构建的第三方库
find_library(CJSON_LIB cjson PATHS ${WEBCFG_CACHE_DIR}/cjson/lib)
find_library(MICROHTTPD_LIB microhttpd PATHS ${WEBCFG_CACHE_DIR}/microhttpd/lib)
find_library(CGIC_LIB cgic PATHS ${WEBCFG_CACHE_DIR}/cgic/lib)

# 子目录
add_subdirectory(src)
add_subdirectory(web)
```

#### 3.3.2 简化的第三方库管理策略
基于可行性分析的改进建议，简化第三方库管理：

```cmake
# 简化的第三方库管理 (cmake/ThirdParty.cmake)
function(add_third_party_library name)
    set(LIB_DIR ${CMAKE_SOURCE_DIR}/third_party/${name})
    set(BUILD_DIR ${CMAKE_BINARY_DIR}/third_party/${name})

    if(EXISTS ${LIB_DIR}/CMakeLists.txt)
        # 使用CMake构建
        add_subdirectory(${LIB_DIR} ${BUILD_DIR})
    elseif(EXISTS ${LIB_DIR}/configure.ac)
        # 使用autotools构建
        include(cmake/AutotoolsBuild.cmake)
        build_with_autotools(${name} ${LIB_DIR} ${BUILD_DIR})
    else()
        # 使用Makefile构建
        include(cmake/MakefileBuild.cmake)
        build_with_makefile(${name} ${LIB_DIR} ${BUILD_DIR})
    endif()
endfunction()

# 添加所需的第三方库
add_third_party_library(cjson)
add_third_party_library(microhttpd)

# 统一的交叉编译配置
function(setup_cross_compile platform)
    if(platform STREQUAL "am335x")
        set(CMAKE_C_COMPILER /opt/platform/am335x/sdk/bin/arm-linux-gnueabihf-gcc)
        set(CMAKE_SYSROOT /opt/platform/am335x/sdk/arm-none-linux-gnueabi/sys-root)
    elseif(platform STREQUAL "zynq")
        set(CMAKE_C_COMPILER /opt/platform/zynq/sdk/bin/arm-linux-gnueabihf-gcc)
        set(CMAKE_SYSROOT /opt/platform/zynq/sdk/arm-none-linux-gnueabi/sys-root)
    endif()

    # 统一的编译选项
    set(CMAKE_C_FLAGS "-Wall -O2 -g")
    set(CMAKE_C_FLAGS_RELEASE "-O3 -DNDEBUG")
    set(CMAKE_C_FLAGS_DEBUG "-O0 -g3 -DDEBUG")
endfunction()
```

## 4. API接口设计（严格对应旧CGI功能）

### 4.1 RESTful API规范

#### 4.1.1 URL设计规范（与旧CGI一一对应）
```
基础URL: http://device-ip/api/v1

配置相关（对应旧CGI配置功能）:
GET    /api/v1/config/center         - 对应0center.c读取功能
POST   /api/v1/config/center         - 对应0center.c保存功能
GET    /api/v1/config/gateway        - 对应0gateway.c读取功能
POST   /api/v1/config/gateway        - 对应0gateway.c保存功能
GET    /api/v1/config/sci            - 对应0sci*.c读取功能
POST   /api/v1/config/sci            - 对应0sci*.c保存功能
GET    /api/v1/config/switch         - 对应0switch*.c读取功能
POST   /api/v1/config/switch         - 对应0switch*.c保存功能
GET    /api/v1/config/recorder       - 对应0recorder.c/0mini.c读取功能
POST   /api/v1/config/recorder       - 对应0recorder.c/0mini.c保存功能

系统相关（对应旧CGI系统功能，功能极简）:
POST   /api/v1/system/reboot         - 对应0system.c重启功能
POST   /api/v1/system/reset          - 对应0system.c重置配置功能
GET    /api/v1/system/logs           - 对应0down.c日志显示功能
GET    /api/v1/system/signal         - 对应0down.c 3G信号检测功能

时间同步（对应0ntp.c，功能极简）:
GET    /api/v1/config/ntp            - 对应0ntp.c读取功能
POST   /api/v1/config/ntp            - 对应0ntp.c保存功能

认证相关（对应0passwd.c，功能极简）:
POST   /api/v1/auth/password         - 对应0passwd.c密码修改功能
```

**重要说明**: 每个API接口都严格对应一个旧CGI程序的功能，不增加额外的接口或功能。

#### 4.1.2 请求响应格式
```json
// 统一请求格式
{
    "action": "save",
    "data": {
        // 具体数据
    }
}

// 统一响应格式
{
    "code": 200,
    "message": "success",
    "data": {
        // 响应数据
    },
    "timestamp": 1635123456
}

// 错误响应格式
{
    "code": 400,
    "message": "参数错误",
    "error": "Invalid IP address format",
    "timestamp": 1635123456
}
```

### 4.2 核心API设计

#### 4.2.1 网络配置API
```c
// 网络配置结构体
typedef struct {
    char ip[16];          // IP地址字符串
    char mask[16];        // 子网掩码
    char gateway[16];     // 网关
    char dns[16];        // DNS服务器
    char mac[18];        // MAC地址
} network_config_t;

// API处理函数
int handle_network_get(struct MHD_Connection *connection, 
                      const char *url, cJSON *request_data);
int handle_network_post(struct MHD_Connection *connection, 
                       const char *url, cJSON *request_data);
```

#### 4.2.2 设备配置API
```c
// 设备配置通用结构
typedef struct {
    char device_type[32]; // 设备类型
    cJSON *config_data;   // 配置数据JSON
} device_config_t;

// API处理函数
int handle_device_get(struct MHD_Connection *connection, 
                     const char *url, cJSON *request_data);
int handle_device_post(struct MHD_Connection *connection, 
                      const char *url, cJSON *request_data);
```

## 5. 数据库设计

### 5.1 配置存储策略
- **保持兼容**: 继续使用现有二进制和ini配置文件格式
- **添加缓存**: 内存中维护配置数据缓存
- **事务支持**: 实现配置修改的回滚机制

### 5.2 配置映射设计
```c
// 配置映射表
typedef struct {
    char *api_path;           // API路径
    char *config_file;        // 配置文件路径
    size_t config_size;       // 配置大小
    int (*validator)(void *); // 数据验证函数
    int (*converter)(cJSON *, void *); // JSON转换函数
} config_mapping_t;

// 配置映射表实例
static config_mapping_t config_mappings[] = {
    {"/config/network", "/etc/eth0-setting", sizeof(stCfgNet), 
     validate_network, convert_network_json},
    {"/config/device/center", CALLCENTERCFG, sizeof(stCfgCenter), 
     validate_center, convert_center_json},
    // ... 其他模块配置映射
};
```

## 6. 安全设计

### 6.1 认证机制
```c
// Token结构设计
typedef struct {
    char token[64];      // 认证token
    time_t expires;      // 过期时间
    char username[32];   // 用户名
    int privileges;      // 权限级别
} auth_token_t;

// 认证中间件
int auth_middleware(struct MHD_Connection *connection, 
                   const char *url, cJSON *request_data);
```

### 6.2 输入验证
```c
// 输入验证函数
int validate_ip_address(const char *ip);
int validate_mac_address(const char *mac);
int validate_port_number(int port);
int validate_config_data(const char *config_type, cJSON *data);
```

## 7. 性能优化

### 7.1 前端优化
- 资源压缩与合并
- 缓存策略
- 按需加载
- 响应式图片

## 8. 兼容性保证

### 8.1 配置文件兼容
- 完全保持二进制配置文件或ini配置文件格式
- 提供配置文件版本检查

### 8.2 业务逻辑兼容
- 保持所有现有业务规则
- 数据验证逻辑不变
- 错误处理机制一致

### 8.3 多平台兼容
- 交叉编译支持
- 平台特定优化
- 硬件适配层抽象

### 8.4 构建系统兼容
- 主项目使用cmake现代构建系统
- 第三方库保持原生构建系统
- 混合构建流程自动化

## 9. 测试策略

### 9.1 单元测试
- 配置读写功能测试
- API接口测试
- 数据验证测试

### 9.2 集成测试
- 前后端集成测试
- 多平台编译测试
- 配置文件兼容性测试
- 第三方库集成测试

### 9.3 系统测试
- 安全性测试

## 10. 部署方案

### 10.1 开发环境
- 自动化构建流水线
- 代码质量检查
- 混合构建系统支持

### 10.2 生产部署
- 一键部署脚本
- 配置备份恢复

### 10.3 升级策略
- 平滑升级机制
- 回滚方案设计

## 11. 优化后的项目里程碑（3阶段方案）

基于可行性分析，采用简化的3阶段实施方案：

### 第一阶段：基础框架搭建 (2周)
**第1周**：
- [ ] 项目结构创建和基础配置
- [ ] 公共工具模块实现 (file_utils, network_utils)
- [ ] 统一配置管理框架
- [ ] 基础HTTP服务器集成

**第2周**：
- [ ] 简化API路由框架实现
- [ ] 认证授权机制
- [ ] 基础测试框架搭建
- [ ] 第一个配置模块实现 (network)

**里程碑1**：基础框架完成，第一个API可用

### 第二阶段：功能实现 (3周)
**第3周**：
- [ ] 核心配置模块实现 (center, gateway, recorder)
- [ ] 系统管理功能实现 (reboot, reset, logs)
- [ ] 前端基础框架重构

**第4周**：
- [ ] 剩余配置模块实现 (sci, switch, ntp)
- [ ] 前端界面完善
- [ ] 单元测试完成

**第5周**：
- [ ] 集成测试和功能验证
- [ ] 多平台编译适配
- [ ] 性能优化

**里程碑2**：所有功能实现完成

### 第三阶段：部署上线 (2周)
**第6周**：
- [ ] 生产环境部署脚本
- [ ] 系统监控和日志
- [ ] 用户验收测试

**第7周**：
- [ ] 文档完善
- [ ] 培训和知识转移
- [ ] 正式上线

**里程碑3**：项目交付完成

### 关键成功因素
- **风险控制**：配置文件格式100%兼容，业务逻辑保持不变
- **质量保证**：代码审查机制，自动化测试覆盖
- **进度管理**：每周进度汇报，里程碑节点控制

这个重构方案在保持100%功能兼容的前提下，新系统的每个功能都必须在旧CGI中找到对应，实现了前后端分离和现代化改造，同时采用混合构建系统确保各组件使用最适合的构建方式，为项目的长期维护和发展奠定了良好基础。 