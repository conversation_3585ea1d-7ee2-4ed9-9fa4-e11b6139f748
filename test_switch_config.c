#include <stdio.h>
#include <stdlib.h>
#include "switch_config.h"
#include <cjson/cJSON.h>

int main() {
    cfg_switch_t config;
    cJSON *json = NULL;
    char *json_string = NULL;
    
    // 设置默认配置
    switch_config_set_default(&config);
    
    // 转换为JSON
    if (switch_config_to_json(&config, &json) != 0) {
        printf("Failed to convert config to JSON\n");
        return -1;
    }
    
    // 转换为字符串
    json_string = cJSON_Print(json);
    if (!json_string) {
        printf("Failed to serialize JSON\n");
        cJSON_Delete(json);
        return -1;
    }
    
    printf("Switch config JSON:\n%s\n", json_string);
    
    // 清理
    free(json_string);
    cJSON_Delete(json);
    
    return 0;
}
